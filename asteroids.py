import pygame
import math
import sys
import random

# Game settings
WIDTH, HEIGHT = 1024, 768
FPS = 60
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)

# Scoring system
SCORE_LARGE_ASTEROID = 20
SCORE_MEDIUM_ASTEROID = 50
SCORE_SMALL_ASTEROID = 100
SCORE_LARGE_SAUCER = 200
SCORE_SMALL_SAUCER = 1000

# Lives system
STARTING_LIVES = 3
INVULNERABILITY_TIME = 120  # frames of invulnerability after respawn
EXTRA_LIFE_SCORE = 10000  # Award extra life every 10,000 points

# Game states
GAME_STATE_START = 0
GAME_STATE_PLAYING = 1
GAME_STATE_PAUSED = 2
GAME_STATE_GAME_OVER = 3

# Ship shape (triangle)
SHIP_SIZE = 30

def draw_ship(surface, x, y, angle, invulnerable=False):
    # Define ship points relative to center
    points = [
        (0, -SHIP_SIZE // 2),
        (SHIP_SIZE // 2, SHIP_SIZE // 2),
        (0, SHIP_SIZE // 4),
        (-SHIP_SIZE // 2, SHIP_SIZE // 2)
    ]
    # Rotate and translate points
    rotated = []
    for px, py in points:
        rx = px * math.cos(angle) - py * math.sin(angle)
        ry = px * math.sin(angle) + py * math.cos(angle)
        rotated.append((x + rx, y + ry))
    # Draw ship (flashing if invulnerable)
    if not invulnerable or (invulnerable and pygame.time.get_ticks() // 100 % 2):
        pygame.draw.polygon(surface, WHITE, rotated, 1)

def respawn_ship():
    """Reset ship position and velocity for respawn"""
    return WIDTH // 2, HEIGHT // 2, 0, 0.0, 0.0

def check_extra_life(score, next_extra_life, lives, sounds_loaded, sound_extra_ship):
    """Check if player earned an extra life"""
    if score >= next_extra_life:
        lives += 1
        next_extra_life += EXTRA_LIFE_SCORE
        if sounds_loaded:
            sound_extra_ship.play()
        return lives, next_extra_life, True
    return lives, next_extra_life, False

def hyperspace_jump():
    """Perform hyperspace jump - teleport to random location with risk"""
    # Random location
    new_x = random.randint(50, WIDTH - 50)
    new_y = random.randint(50, HEIGHT - 50)

    # Small chance of destruction (about 10% like original)
    destroyed = random.random() < 0.1

    return new_x, new_y, 0, 0.0, 0.0, destroyed

def draw_ui(surface, font, score, lives):
    """Draw the game UI (score and lives)"""
    # Draw score
    score_text = font.render(f"SCORE: {score}", True, WHITE)
    surface.blit(score_text, (10, 10))

    # Draw lives
    lives_text = font.render(f"LIVES: {lives}", True, WHITE)
    surface.blit(lives_text, (10, 40))

def draw_game_over(surface, font, score):
    """Draw game over screen"""
    game_over_text = font.render("GAME OVER", True, WHITE)
    score_text = font.render(f"FINAL SCORE: {score}", True, WHITE)
    restart_text = font.render("Press R to restart or ESC to quit", True, WHITE)

    # Center the text
    game_over_rect = game_over_text.get_rect(center=(WIDTH // 2, HEIGHT // 2 - 50))
    score_rect = score_text.get_rect(center=(WIDTH // 2, HEIGHT // 2))
    restart_rect = restart_text.get_rect(center=(WIDTH // 2, HEIGHT // 2 + 50))

    surface.blit(game_over_text, game_over_rect)
    surface.blit(score_text, score_rect)
    surface.blit(restart_text, restart_rect)

def draw_start_screen(surface, font):
    """Draw start screen"""
    title_font = pygame.font.Font(None, 72)
    title_text = title_font.render("ASTEROIDS", True, WHITE)

    instructions = [
        "Arrow Keys: Rotate and Thrust",
        "Spacebar: Fire",
        "H: Hyperspace (Emergency Only!)",
        "ESC: Pause Game",
        "",
        "Press SPACE to Start"
    ]

    # Center the title
    title_rect = title_text.get_rect(center=(WIDTH // 2, HEIGHT // 2 - 150))
    surface.blit(title_text, title_rect)

    # Draw instructions
    for i, instruction in enumerate(instructions):
        if instruction:  # Skip empty lines
            text = font.render(instruction, True, WHITE)
            text_rect = text.get_rect(center=(WIDTH // 2, HEIGHT // 2 - 50 + i * 40))
            surface.blit(text, text_rect)

def draw_pause_screen(surface, font):
    """Draw pause screen"""
    pause_text = pygame.font.Font(None, 72).render("PAUSED", True, WHITE)
    continue_text = font.render("Press ESC to continue", True, WHITE)

    # Center the text
    pause_rect = pause_text.get_rect(center=(WIDTH // 2, HEIGHT // 2 - 50))
    continue_rect = continue_text.get_rect(center=(WIDTH // 2, HEIGHT // 2 + 50))

    surface.blit(pause_text, pause_rect)
    surface.blit(continue_text, continue_rect)

# Asteroid settings
ASTEROID_MIN_SIZE = 20  # Smallest
ASTEROID_MEDIUM_SIZE = 40  # Medium
ASTEROID_MAX_SIZE = 60  # Large
ASTEROID_MIN_SPEED = 0.5
ASTEROID_MAX_SPEED = 2.0

class Asteroid:
    def __init__(self, x, y, size=None, dx=None, dy=None):
        self.size = size if size else random.choice([ASTEROID_MAX_SIZE, ASTEROID_MEDIUM_SIZE, ASTEROID_MIN_SIZE])
        self.x = x
        self.y = y
        angle = random.uniform(0, 2 * math.pi)
        speed = random.uniform(ASTEROID_MIN_SPEED, ASTEROID_MAX_SPEED)
        self.dx = dx if dx is not None else math.cos(angle) * speed
        self.dy = dy if dy is not None else math.sin(angle) * speed
        self.points = self.generate_shape()

    def generate_shape(self):
        # Generate a random jagged polygon for the asteroid
        points = []
        verts = random.randint(8, 12)
        for i in range(verts):
            angle = 2 * math.pi * i / verts
            radius = self.size * random.uniform(0.7, 1.0)
            x = math.cos(angle) * radius
            y = math.sin(angle) * radius
            points.append((x, y))
        return points

    def move(self):
        self.x = (self.x + self.dx) % WIDTH
        self.y = (self.y + self.dy) % HEIGHT

    def draw(self, surface):
        # Draw the asteroid as a polygon
        transformed = [(self.x + px, self.y + py) for (px, py) in self.points]
        pygame.draw.polygon(surface, WHITE, transformed, 1)

# Bullet settings
BULLET_SPEED = 8
BULLET_LIFETIME = 60  # frames
MAX_BULLETS = 4

class Bullet:
    def __init__(self, x, y, angle):
        self.x = x
        self.y = y
        self.dx = math.sin(angle) * BULLET_SPEED
        self.dy = -math.cos(angle) * BULLET_SPEED
        self.lifetime = BULLET_LIFETIME

    def move(self):
        self.x = (self.x + self.dx) % WIDTH
        self.y = (self.y + self.dy) % HEIGHT
        self.lifetime -= 1

    def draw(self, surface):
        pygame.draw.circle(surface, WHITE, (int(self.x), int(self.y)), 2)

# Saucer settings
SAUCER_LARGE_SIZE = 40
SAUCER_SMALL_SIZE = 20
SAUCER_SPEED = 2
SAUCER_SHOOT_INTERVAL = 60  # frames between shots

class Saucer:
    def __init__(self, is_small=False):
        self.is_small = is_small
        self.size = SAUCER_SMALL_SIZE if is_small else SAUCER_LARGE_SIZE

        # Start from random side of screen
        side = random.randint(0, 3)
        if side == 0:  # Left
            self.x = -self.size
            self.y = random.randint(self.size, HEIGHT - self.size)
            self.dx = SAUCER_SPEED
            self.dy = random.uniform(-0.5, 0.5)
        elif side == 1:  # Right
            self.x = WIDTH + self.size
            self.y = random.randint(self.size, HEIGHT - self.size)
            self.dx = -SAUCER_SPEED
            self.dy = random.uniform(-0.5, 0.5)
        elif side == 2:  # Top
            self.x = random.randint(self.size, WIDTH - self.size)
            self.y = -self.size
            self.dx = random.uniform(-0.5, 0.5)
            self.dy = SAUCER_SPEED
        else:  # Bottom
            self.x = random.randint(self.size, WIDTH - self.size)
            self.y = HEIGHT + self.size
            self.dx = random.uniform(-0.5, 0.5)
            self.dy = -SAUCER_SPEED

        self.shoot_timer = random.randint(30, SAUCER_SHOOT_INTERVAL)
        self.bullets = []

    def move(self):
        self.x = (self.x + self.dx) % WIDTH
        self.y = (self.y + self.dy) % HEIGHT

        # Update bullets
        for bullet in self.bullets:
            bullet.move()
        self.bullets = [b for b in self.bullets if b.lifetime > 0]

        # Update shoot timer
        self.shoot_timer -= 1

    def shoot(self, target_x=None, target_y=None):
        """Shoot at target (small saucer) or random direction (large saucer)"""
        if self.shoot_timer <= 0:
            if self.is_small and target_x is not None and target_y is not None:
                # Small saucer aims at player
                angle = math.atan2(target_y - self.y, target_x - self.x)
                # Add some inaccuracy
                angle += random.uniform(-0.2, 0.2)
            else:
                # Large saucer shoots randomly
                angle = random.uniform(0, 2 * math.pi)

            # Create bullet
            bullet_dx = math.cos(angle) * BULLET_SPEED
            bullet_dy = math.sin(angle) * BULLET_SPEED
            saucer_bullet = Bullet(self.x, self.y, 0)  # Angle doesn't matter, we'll override velocity
            saucer_bullet.dx = bullet_dx
            saucer_bullet.dy = bullet_dy
            self.bullets.append(saucer_bullet)

            self.shoot_timer = SAUCER_SHOOT_INTERVAL
            return True
        return False

    def draw(self, surface):
        # Draw saucer as two ellipses (classic UFO shape)
        # Main body
        pygame.draw.ellipse(surface, WHITE,
                          (self.x - self.size//2, self.y - self.size//4,
                           self.size, self.size//2), 1)
        # Top dome
        pygame.draw.ellipse(surface, WHITE,
                          (self.x - self.size//3, self.y - self.size//3,
                           self.size//1.5, self.size//3), 1)

        # Draw saucer bullets
        for bullet in self.bullets:
            bullet.draw(surface)

    def is_off_screen(self):
        """Check if saucer has moved completely off screen"""
        return (self.x < -self.size * 2 or self.x > WIDTH + self.size * 2 or
                self.y < -self.size * 2 or self.y > HEIGHT + self.size * 2)

def main():
    pygame.init()
    pygame.mixer.init()
    screen = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("Asteroids")
    clock = pygame.time.Clock()
    font = pygame.font.Font(None, 36)

    # Load sound effects
    try:
        sound_fire = pygame.mixer.Sound("assets/fire.wav")
        sound_thrust = pygame.mixer.Sound("assets/thrust.wav")
        sound_bang_large = pygame.mixer.Sound("assets/bangLarge.wav")
        sound_bang_medium = pygame.mixer.Sound("assets/bangMedium.wav")
        sound_bang_small = pygame.mixer.Sound("assets/bangSmall.wav")
        sound_extra_ship = pygame.mixer.Sound("assets/extraShip.wav")
        sound_beat1 = pygame.mixer.Sound("assets/beat1.wav")
        sound_beat2 = pygame.mixer.Sound("assets/beat2.wav")

        # Set volume levels
        sound_fire.set_volume(0.3)
        sound_thrust.set_volume(0.2)
        sound_bang_large.set_volume(0.4)
        sound_bang_medium.set_volume(0.4)
        sound_bang_small.set_volume(0.4)
        sound_extra_ship.set_volume(0.5)
        sound_beat1.set_volume(0.3)
        sound_beat2.set_volume(0.3)

        sounds_loaded = True
    except pygame.error:
        print("Could not load sound files. Game will run without sound.")
        sounds_loaded = False

    # Game state
    game_state = GAME_STATE_START
    score = 0
    lives = STARTING_LIVES
    invulnerable_timer = 0
    next_extra_life = EXTRA_LIFE_SCORE

    # Sound state
    thrust_playing = False
    heartbeat_timer = 0
    heartbeat_beat = 0  # 0 for beat1, 1 for beat2

    # Saucer state
    saucer = None
    saucer_spawn_timer = random.randint(600, 1200)  # 10-20 seconds at 60 FPS

    ship_x, ship_y = WIDTH // 2, HEIGHT // 2
    ship_angle = 0
    ship_dx, ship_dy = 0.0, 0.0
    ship_thrust = 0.15
    friction = 0.99

    # Spawn 4-6 large asteroids
    asteroids = []
    for _ in range(random.randint(4, 6)):
        # Avoid spawning in the center
        while True:
            x = random.randint(0, WIDTH)
            y = random.randint(0, HEIGHT)
            if abs(x - ship_x) > 100 and abs(y - ship_y) > 100:
                break
        asteroids.append(Asteroid(x, y, size=ASTEROID_MAX_SIZE))

    bullets = []
    shoot_cooldown = 0

    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            if event.type == pygame.KEYDOWN:
                if game_state == GAME_STATE_START:
                    if event.key == pygame.K_SPACE:
                        # Start the game
                        game_state = GAME_STATE_PLAYING
                elif game_state == GAME_STATE_PLAYING:
                    if event.key == pygame.K_ESCAPE:
                        # Pause the game
                        game_state = GAME_STATE_PAUSED
                        if sounds_loaded and thrust_playing:
                            sound_thrust.stop()
                            thrust_playing = False
                    elif event.key == pygame.K_SPACE and len(bullets) < MAX_BULLETS and shoot_cooldown == 0:
                        # Fire bullet from ship's nose
                        nose_x = ship_x + math.sin(ship_angle) * SHIP_SIZE // 2
                        nose_y = ship_y - math.cos(ship_angle) * SHIP_SIZE // 2
                        bullets.append(Bullet(nose_x, nose_y, ship_angle))
                        shoot_cooldown = 8  # frames between shots
                        # Play fire sound
                        if sounds_loaded:
                            sound_fire.play()
                    elif event.key == pygame.K_h:
                        # Hyperspace jump
                        ship_x, ship_y, ship_angle, ship_dx, ship_dy, destroyed = hyperspace_jump()
                        if destroyed:
                            # Ship destroyed by hyperspace
                            lives -= 1
                            if sounds_loaded and thrust_playing:
                                sound_thrust.stop()
                                thrust_playing = False
                            if lives <= 0:
                                game_state = GAME_STATE_GAME_OVER
                                if sounds_loaded:
                                    pygame.mixer.stop()
                            else:
                                # Respawn ship
                                ship_x, ship_y, ship_angle, ship_dx, ship_dy = respawn_ship()
                                invulnerable_timer = INVULNERABILITY_TIME
                                if sounds_loaded:
                                    sound_extra_ship.play()
                        else:
                            # Successful hyperspace jump - brief invulnerability
                            invulnerable_timer = 60
                elif game_state == GAME_STATE_PAUSED:
                    if event.key == pygame.K_ESCAPE:
                        # Unpause the game
                        game_state = GAME_STATE_PLAYING
                elif game_state == GAME_STATE_GAME_OVER:
                    if event.key == pygame.K_r:
                        # Restart game
                        game_state = GAME_STATE_PLAYING
                        score = 0
                        lives = STARTING_LIVES
                        invulnerable_timer = 0
                        next_extra_life = EXTRA_LIFE_SCORE
                        ship_x, ship_y, ship_angle, ship_dx, ship_dy = respawn_ship()

                        # Reset sound state
                        thrust_playing = False
                        heartbeat_timer = 0
                        heartbeat_beat = 0
                        if sounds_loaded:
                            pygame.mixer.stop()

                        # Reset saucer state
                        saucer = None
                        saucer_spawn_timer = random.randint(600, 1200)

                        # Respawn asteroids
                        asteroids = []
                        for _ in range(random.randint(4, 6)):
                            while True:
                                x = random.randint(0, WIDTH)
                                y = random.randint(0, HEIGHT)
                                if abs(x - ship_x) > 100 and abs(y - ship_y) > 100:
                                    break
                            asteroids.append(Asteroid(x, y, size=ASTEROID_MAX_SIZE))
                        bullets = []
                        shoot_cooldown = 0
                    elif event.key == pygame.K_ESCAPE:
                        running = False

        # Only update game logic if playing
        if game_state == GAME_STATE_PLAYING:
            keys = pygame.key.get_pressed()
            if keys[pygame.K_LEFT]:
                ship_angle -= 0.07
            if keys[pygame.K_RIGHT]:
                ship_angle += 0.07
            if keys[pygame.K_UP]:
                ship_dx += math.sin(ship_angle) * ship_thrust
                ship_dy -= math.cos(ship_angle) * ship_thrust
                # Play thrust sound
                if sounds_loaded and not thrust_playing:
                    sound_thrust.play(-1)  # Loop indefinitely
                    thrust_playing = True
            else:
                # Stop thrust sound when not thrusting
                if sounds_loaded and thrust_playing:
                    sound_thrust.stop()
                    thrust_playing = False

            ship_dx *= friction
            ship_dy *= friction
            ship_x = (ship_x + ship_dx) % WIDTH
            ship_y = (ship_y + ship_dy) % HEIGHT

            # Update invulnerability timer
            if invulnerable_timer > 0:
                invulnerable_timer -= 1

            # Update bullets
            for bullet in bullets:
                bullet.move()
            bullets = [b for b in bullets if b.lifetime > 0]
            if shoot_cooldown > 0:
                shoot_cooldown -= 1

            # Update heartbeat sound
            if sounds_loaded:
                heartbeat_timer += 1
                # Heartbeat speed depends on number of asteroids (faster with fewer asteroids)
                heartbeat_interval = max(30, 120 - len(asteroids) * 10)
                if heartbeat_timer >= heartbeat_interval:
                    if heartbeat_beat == 0:
                        sound_beat1.play()
                        heartbeat_beat = 1
                    else:
                        sound_beat2.play()
                        heartbeat_beat = 0
                    heartbeat_timer = 0

            # Update saucer spawning
            saucer_spawn_timer -= 1
            if saucer_spawn_timer <= 0 and saucer is None:
                # Spawn small saucer after 10,000 points, large saucer before
                is_small_saucer = score >= 10000
                saucer = Saucer(is_small_saucer)
                saucer_spawn_timer = random.randint(600, 1200)

            # Update saucer
            if saucer:
                saucer.move()
                # Saucer shoots at player
                if saucer.shoot(ship_x, ship_y) and sounds_loaded:
                    sound_fire.play()

                # Remove saucer if it goes off screen
                if saucer.is_off_screen():
                    saucer = None

            # Collision detection: bullets vs asteroids
            new_asteroids = []
            for asteroid in asteroids:
                hit = False
                for bullet in bullets:
                    dist = math.hypot(asteroid.x - bullet.x, asteroid.y - bullet.y)
                    if dist < asteroid.size:
                        bullets.remove(bullet)
                        hit = True
                        break
                if hit:
                    # Add score based on asteroid size and play explosion sound
                    if asteroid.size == ASTEROID_MAX_SIZE:
                        score += SCORE_LARGE_ASTEROID
                        if sounds_loaded:
                            sound_bang_large.play()
                        # Large asteroid breaks into 2 mediums
                        for _ in range(2):
                            new_asteroids.append(Asteroid(asteroid.x, asteroid.y, size=ASTEROID_MEDIUM_SIZE))
                    elif asteroid.size == ASTEROID_MEDIUM_SIZE:
                        score += SCORE_MEDIUM_ASTEROID
                        if sounds_loaded:
                            sound_bang_medium.play()
                        # Medium asteroid breaks into 3-4 smalls
                        for _ in range(random.randint(3, 4)):
                            new_asteroids.append(Asteroid(asteroid.x, asteroid.y, size=ASTEROID_MIN_SIZE))
                    else:  # Small asteroid
                        score += SCORE_SMALL_ASTEROID
                        if sounds_loaded:
                            sound_bang_small.play()
                        # Small asteroid just disappears

                    # Check for extra life
                    lives, next_extra_life, _ = check_extra_life(score, next_extra_life, lives, sounds_loaded, sound_extra_ship)

                    continue  # Don't add the destroyed asteroid
                new_asteroids.append(asteroid)
            asteroids = new_asteroids

            # Collision detection: bullets vs saucer
            if saucer:
                for bullet in bullets:
                    dist = math.hypot(saucer.x - bullet.x, saucer.y - bullet.y)
                    if dist < saucer.size:
                        bullets.remove(bullet)
                        # Add score for saucer
                        if saucer.is_small:
                            score += SCORE_SMALL_SAUCER
                            if sounds_loaded:
                                sound_bang_small.play()
                        else:
                            score += SCORE_LARGE_SAUCER
                            if sounds_loaded:
                                sound_bang_medium.play()

                        # Check for extra life
                        lives, next_extra_life, _ = check_extra_life(score, next_extra_life, lives, sounds_loaded, sound_extra_ship)

                        saucer = None
                        break

            # Check if all asteroids are destroyed - spawn new wave
            if len(asteroids) == 0:
                for _ in range(random.randint(4, 6)):
                    while True:
                        x = random.randint(0, WIDTH)
                        y = random.randint(0, HEIGHT)
                        if abs(x - ship_x) > 100 and abs(y - ship_y) > 100:
                            break
                    asteroids.append(Asteroid(x, y, size=ASTEROID_MAX_SIZE))

            # Collision detection: ship vs asteroids (only if not invulnerable)
            if invulnerable_timer == 0:
                ship_hit = False

                # Check asteroid collisions
                for asteroid in asteroids:
                    dist = math.hypot(asteroid.x - ship_x, asteroid.y - ship_y)
                    if dist < asteroid.size + SHIP_SIZE // 2:
                        ship_hit = True
                        break

                # Check saucer collision
                if saucer and not ship_hit:
                    dist = math.hypot(saucer.x - ship_x, saucer.y - ship_y)
                    if dist < saucer.size + SHIP_SIZE // 2:
                        ship_hit = True

                # Check saucer bullet collisions
                if saucer and not ship_hit:
                    for bullet in saucer.bullets:
                        dist = math.hypot(bullet.x - ship_x, bullet.y - ship_y)
                        if dist < SHIP_SIZE // 2 + 2:  # bullet radius is 2
                            ship_hit = True
                            saucer.bullets.remove(bullet)
                            break

                if ship_hit:
                    lives -= 1
                    # Stop thrust sound if playing
                    if sounds_loaded and thrust_playing:
                        sound_thrust.stop()
                        thrust_playing = False
                    if lives <= 0:
                        game_state = GAME_STATE_GAME_OVER
                        # Stop all sounds when game over
                        if sounds_loaded:
                            pygame.mixer.stop()
                    else:
                        # Respawn ship
                        ship_x, ship_y, ship_angle, ship_dx, ship_dy = respawn_ship()
                        invulnerable_timer = INVULNERABILITY_TIME
                        if sounds_loaded:
                            sound_extra_ship.play()

        screen.fill(BLACK)

        if game_state == GAME_STATE_START:
            draw_start_screen(screen, font)
        elif game_state == GAME_STATE_PLAYING:
            for asteroid in asteroids:
                asteroid.move()
                asteroid.draw(screen)
            for bullet in bullets:
                bullet.draw(screen)
            if saucer:
                saucer.draw(screen)
            draw_ship(screen, ship_x, ship_y, ship_angle, invulnerable_timer > 0)
            draw_ui(screen, font, score, lives)
        elif game_state == GAME_STATE_PAUSED:
            # Draw the game state but paused
            for asteroid in asteroids:
                asteroid.draw(screen)
            for bullet in bullets:
                bullet.draw(screen)
            if saucer:
                saucer.draw(screen)
            draw_ship(screen, ship_x, ship_y, ship_angle, invulnerable_timer > 0)
            draw_ui(screen, font, score, lives)
            draw_pause_screen(screen, font)
        elif game_state == GAME_STATE_GAME_OVER:
            draw_game_over(screen, font, score)

        pygame.display.flip()
        clock.tick(FPS)

    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()
