import pygame
import math
import sys
import random

# Game settings
WIDTH, HEIGHT = 800, 600
FPS = 60
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)

# Ship shape (triangle)
SHIP_SIZE = 30

def draw_ship(surface, x, y, angle):
    # Define ship points relative to center
    points = [
        (0, -SHIP_SIZE // 2),
        (SHIP_SIZE // 2, SHIP_SIZE // 2),
        (0, SHIP_SIZE // 4),
        (-SHIP_SIZE // 2, SHIP_SIZE // 2)
    ]
    # Rotate and translate points
    rotated = []
    for px, py in points:
        rx = px * math.cos(angle) - py * math.sin(angle)
        ry = px * math.sin(angle) + py * math.cos(angle)
        rotated.append((x + rx, y + ry))
    # Draw ship
    pygame.draw.polygon(surface, WHITE, rotated, 1)

# Asteroid settings
ASTEROID_MIN_SIZE = 20  # Smallest
ASTEROID_MEDIUM_SIZE = 40  # Medium
ASTEROID_MAX_SIZE = 60  # Large
ASTEROID_MIN_SPEED = 1
ASTEROID_MAX_SPEED = 3

class Asteroid:
    def __init__(self, x, y, size=None, dx=None, dy=None):
        self.size = size if size else random.choice([ASTEROID_MAX_SIZE, ASTEROID_MEDIUM_SIZE, ASTEROID_MIN_SIZE])
        self.x = x
        self.y = y
        angle = random.uniform(0, 2 * math.pi)
        speed = random.uniform(ASTEROID_MIN_SPEED, ASTEROID_MAX_SPEED)
        self.dx = dx if dx is not None else math.cos(angle) * speed
        self.dy = dy if dy is not None else math.sin(angle) * speed
        self.points = self.generate_shape()

    def generate_shape(self):
        # Generate a random jagged polygon for the asteroid
        points = []
        verts = random.randint(8, 12)
        for i in range(verts):
            angle = 2 * math.pi * i / verts
            radius = self.size * random.uniform(0.7, 1.0)
            x = math.cos(angle) * radius
            y = math.sin(angle) * radius
            points.append((x, y))
        return points

    def move(self):
        self.x = (self.x + self.dx) % WIDTH
        self.y = (self.y + self.dy) % HEIGHT

    def draw(self, surface):
        # Draw the asteroid as a polygon
        transformed = [(self.x + px, self.y + py) for (px, py) in self.points]
        pygame.draw.polygon(surface, WHITE, transformed, 1)

# Bullet settings
BULLET_SPEED = 8
BULLET_LIFETIME = 60  # frames
MAX_BULLETS = 4

class Bullet:
    def __init__(self, x, y, angle):
        self.x = x
        self.y = y
        self.dx = math.sin(angle) * BULLET_SPEED
        self.dy = -math.cos(angle) * BULLET_SPEED
        self.lifetime = BULLET_LIFETIME

    def move(self):
        self.x = (self.x + self.dx) % WIDTH
        self.y = (self.y + self.dy) % HEIGHT
        self.lifetime -= 1

    def draw(self, surface):
        pygame.draw.circle(surface, WHITE, (int(self.x), int(self.y)), 2)

def main():
    pygame.init()
    screen = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("Asteroids")
    clock = pygame.time.Clock()
    
    ship_x, ship_y = WIDTH // 2, HEIGHT // 2
    ship_angle = 0
    ship_dx, ship_dy = 0.0, 0.0
    ship_thrust = 0.15
    friction = 0.99

    # Spawn 4-6 large asteroids
    asteroids = []
    for _ in range(random.randint(4, 6)):
        # Avoid spawning in the center
        while True:
            x = random.randint(0, WIDTH)
            y = random.randint(0, HEIGHT)
            if abs(x - ship_x) > 100 and abs(y - ship_y) > 100:
                break
        asteroids.append(Asteroid(x, y, size=ASTEROID_MAX_SIZE))

    bullets = []
    shoot_cooldown = 0

    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_SPACE and len(bullets) < MAX_BULLETS and shoot_cooldown == 0:
                    # Fire bullet from ship's nose
                    nose_x = ship_x + math.sin(ship_angle) * SHIP_SIZE // 2
                    nose_y = ship_y - math.cos(ship_angle) * SHIP_SIZE // 2
                    bullets.append(Bullet(nose_x, nose_y, ship_angle))
                    shoot_cooldown = 8  # frames between shots

        keys = pygame.key.get_pressed()
        if keys[pygame.K_LEFT]:
            ship_angle -= 0.07
        if keys[pygame.K_RIGHT]:
            ship_angle += 0.07
        if keys[pygame.K_UP]:
            ship_dx += math.sin(ship_angle) * ship_thrust
            ship_dy -= math.cos(ship_angle) * ship_thrust

        ship_dx *= friction
        ship_dy *= friction
        ship_x = (ship_x + ship_dx) % WIDTH
        ship_y = (ship_y + ship_dy) % HEIGHT

        # Update bullets
        for bullet in bullets:
            bullet.move()
        bullets = [b for b in bullets if b.lifetime > 0]
        if shoot_cooldown > 0:
            shoot_cooldown -= 1

        # Collision detection: bullets vs asteroids
        new_asteroids = []
        for asteroid in asteroids:
            hit = False
            for bullet in bullets:
                dist = math.hypot(asteroid.x - bullet.x, asteroid.y - bullet.y)
                if dist < asteroid.size:
                    bullets.remove(bullet)
                    hit = True
                    break
            if hit:
                # Large asteroid breaks into 2 mediums
                if asteroid.size == ASTEROID_MAX_SIZE:
                    for _ in range(2):
                        new_asteroids.append(Asteroid(asteroid.x, asteroid.y, size=ASTEROID_MEDIUM_SIZE))
                # Medium asteroid breaks into 3-4 smalls
                elif asteroid.size == ASTEROID_MEDIUM_SIZE:
                    for _ in range(random.randint(3, 4)):
                        new_asteroids.append(Asteroid(asteroid.x, asteroid.y, size=ASTEROID_MIN_SIZE))
                # Small asteroid just disappears
                continue  # Don't add the destroyed asteroid
            new_asteroids.append(asteroid)
        asteroids = new_asteroids

        # Collision detection: ship vs asteroids
        for asteroid in asteroids:
            dist = math.hypot(asteroid.x - ship_x, asteroid.y - ship_y)
            if dist < asteroid.size + SHIP_SIZE // 2:
                # End game or respawn logic could go here
                running = False

        screen.fill(BLACK)
        for asteroid in asteroids:
            asteroid.move()
            asteroid.draw(screen)
        for bullet in bullets:
            bullet.draw(screen)
        draw_ship(screen, ship_x, ship_y, ship_angle)
        pygame.display.flip()
        clock.tick(FPS)

    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()
