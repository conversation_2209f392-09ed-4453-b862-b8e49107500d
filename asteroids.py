import pygame
import math
import sys
import random

# Game settings
WIDTH, HEIGHT = 1024, 768
FPS = 60
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)

# Scoring system
SCORE_LARGE_ASTEROID = 20
SCORE_MEDIUM_ASTEROID = 50
SCORE_SMALL_ASTEROID = 100

# Lives system
STARTING_LIVES = 3
INVULNERABILITY_TIME = 120  # frames of invulnerability after respawn

# Ship shape (triangle)
SHIP_SIZE = 30

def draw_ship(surface, x, y, angle, invulnerable=False):
    # Define ship points relative to center
    points = [
        (0, -SHIP_SIZE // 2),
        (SHIP_SIZE // 2, SHIP_SIZE // 2),
        (0, SHIP_SIZE // 4),
        (-SHIP_SIZE // 2, SHIP_SIZE // 2)
    ]
    # Rotate and translate points
    rotated = []
    for px, py in points:
        rx = px * math.cos(angle) - py * math.sin(angle)
        ry = px * math.sin(angle) + py * math.cos(angle)
        rotated.append((x + rx, y + ry))
    # Draw ship (flashing if invulnerable)
    if not invulnerable or (invulnerable and pygame.time.get_ticks() // 100 % 2):
        pygame.draw.polygon(surface, WHITE, rotated, 1)

def respawn_ship():
    """Reset ship position and velocity for respawn"""
    return WIDTH // 2, HEIGHT // 2, 0, 0.0, 0.0

def draw_ui(surface, font, score, lives):
    """Draw the game UI (score and lives)"""
    # Draw score
    score_text = font.render(f"SCORE: {score}", True, WHITE)
    surface.blit(score_text, (10, 10))

    # Draw lives
    lives_text = font.render(f"LIVES: {lives}", True, WHITE)
    surface.blit(lives_text, (10, 40))

def draw_game_over(surface, font, score):
    """Draw game over screen"""
    game_over_text = font.render("GAME OVER", True, WHITE)
    score_text = font.render(f"FINAL SCORE: {score}", True, WHITE)
    restart_text = font.render("Press R to restart or ESC to quit", True, WHITE)

    # Center the text
    game_over_rect = game_over_text.get_rect(center=(WIDTH // 2, HEIGHT // 2 - 50))
    score_rect = score_text.get_rect(center=(WIDTH // 2, HEIGHT // 2))
    restart_rect = restart_text.get_rect(center=(WIDTH // 2, HEIGHT // 2 + 50))

    surface.blit(game_over_text, game_over_rect)
    surface.blit(score_text, score_rect)
    surface.blit(restart_text, restart_rect)

# Asteroid settings
ASTEROID_MIN_SIZE = 20  # Smallest
ASTEROID_MEDIUM_SIZE = 40  # Medium
ASTEROID_MAX_SIZE = 60  # Large
ASTEROID_MIN_SPEED = 0.5
ASTEROID_MAX_SPEED = 2.0

class Asteroid:
    def __init__(self, x, y, size=None, dx=None, dy=None):
        self.size = size if size else random.choice([ASTEROID_MAX_SIZE, ASTEROID_MEDIUM_SIZE, ASTEROID_MIN_SIZE])
        self.x = x
        self.y = y
        angle = random.uniform(0, 2 * math.pi)
        speed = random.uniform(ASTEROID_MIN_SPEED, ASTEROID_MAX_SPEED)
        self.dx = dx if dx is not None else math.cos(angle) * speed
        self.dy = dy if dy is not None else math.sin(angle) * speed
        self.points = self.generate_shape()

    def generate_shape(self):
        # Generate a random jagged polygon for the asteroid
        points = []
        verts = random.randint(8, 12)
        for i in range(verts):
            angle = 2 * math.pi * i / verts
            radius = self.size * random.uniform(0.7, 1.0)
            x = math.cos(angle) * radius
            y = math.sin(angle) * radius
            points.append((x, y))
        return points

    def move(self):
        self.x = (self.x + self.dx) % WIDTH
        self.y = (self.y + self.dy) % HEIGHT

    def draw(self, surface):
        # Draw the asteroid as a polygon
        transformed = [(self.x + px, self.y + py) for (px, py) in self.points]
        pygame.draw.polygon(surface, WHITE, transformed, 1)

# Bullet settings
BULLET_SPEED = 8
BULLET_LIFETIME = 60  # frames
MAX_BULLETS = 4

class Bullet:
    def __init__(self, x, y, angle):
        self.x = x
        self.y = y
        self.dx = math.sin(angle) * BULLET_SPEED
        self.dy = -math.cos(angle) * BULLET_SPEED
        self.lifetime = BULLET_LIFETIME

    def move(self):
        self.x = (self.x + self.dx) % WIDTH
        self.y = (self.y + self.dy) % HEIGHT
        self.lifetime -= 1

    def draw(self, surface):
        pygame.draw.circle(surface, WHITE, (int(self.x), int(self.y)), 2)

def main():
    pygame.init()
    pygame.mixer.init()
    screen = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("Asteroids")
    clock = pygame.time.Clock()
    font = pygame.font.Font(None, 36)

    # Load sound effects
    try:
        sound_fire = pygame.mixer.Sound("assets/fire.wav")
        sound_thrust = pygame.mixer.Sound("assets/thrust.wav")
        sound_bang_large = pygame.mixer.Sound("assets/bangLarge.wav")
        sound_bang_medium = pygame.mixer.Sound("assets/bangMedium.wav")
        sound_bang_small = pygame.mixer.Sound("assets/bangSmall.wav")
        sound_extra_ship = pygame.mixer.Sound("assets/extraShip.wav")
        sound_beat1 = pygame.mixer.Sound("assets/beat1.wav")
        sound_beat2 = pygame.mixer.Sound("assets/beat2.wav")

        # Set volume levels
        sound_fire.set_volume(0.3)
        sound_thrust.set_volume(0.2)
        sound_bang_large.set_volume(0.4)
        sound_bang_medium.set_volume(0.4)
        sound_bang_small.set_volume(0.4)
        sound_extra_ship.set_volume(0.5)
        sound_beat1.set_volume(0.3)
        sound_beat2.set_volume(0.3)

        sounds_loaded = True
    except pygame.error:
        print("Could not load sound files. Game will run without sound.")
        sounds_loaded = False

    # Game state
    score = 0
    lives = STARTING_LIVES
    invulnerable_timer = 0
    game_over = False

    # Sound state
    thrust_playing = False
    heartbeat_timer = 0
    heartbeat_beat = 0  # 0 for beat1, 1 for beat2

    ship_x, ship_y = WIDTH // 2, HEIGHT // 2
    ship_angle = 0
    ship_dx, ship_dy = 0.0, 0.0
    ship_thrust = 0.15
    friction = 0.99

    # Spawn 4-6 large asteroids
    asteroids = []
    for _ in range(random.randint(4, 6)):
        # Avoid spawning in the center
        while True:
            x = random.randint(0, WIDTH)
            y = random.randint(0, HEIGHT)
            if abs(x - ship_x) > 100 and abs(y - ship_y) > 100:
                break
        asteroids.append(Asteroid(x, y, size=ASTEROID_MAX_SIZE))

    bullets = []
    shoot_cooldown = 0

    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            if event.type == pygame.KEYDOWN:
                if game_over:
                    if event.key == pygame.K_r:
                        # Restart game
                        score = 0
                        lives = STARTING_LIVES
                        invulnerable_timer = 0
                        game_over = False
                        ship_x, ship_y, ship_angle, ship_dx, ship_dy = respawn_ship()

                        # Reset sound state
                        thrust_playing = False
                        heartbeat_timer = 0
                        heartbeat_beat = 0
                        if sounds_loaded:
                            pygame.mixer.stop()

                        # Respawn asteroids
                        asteroids = []
                        for _ in range(random.randint(4, 6)):
                            while True:
                                x = random.randint(0, WIDTH)
                                y = random.randint(0, HEIGHT)
                                if abs(x - ship_x) > 100 and abs(y - ship_y) > 100:
                                    break
                            asteroids.append(Asteroid(x, y, size=ASTEROID_MAX_SIZE))
                        bullets = []
                        shoot_cooldown = 0
                    elif event.key == pygame.K_ESCAPE:
                        running = False
                elif event.key == pygame.K_SPACE and len(bullets) < MAX_BULLETS and shoot_cooldown == 0:
                    # Fire bullet from ship's nose
                    nose_x = ship_x + math.sin(ship_angle) * SHIP_SIZE // 2
                    nose_y = ship_y - math.cos(ship_angle) * SHIP_SIZE // 2
                    bullets.append(Bullet(nose_x, nose_y, ship_angle))
                    shoot_cooldown = 8  # frames between shots
                    # Play fire sound
                    if sounds_loaded:
                        sound_fire.play()

        # Only update game logic if not game over
        if not game_over:
            keys = pygame.key.get_pressed()
            if keys[pygame.K_LEFT]:
                ship_angle -= 0.07
            if keys[pygame.K_RIGHT]:
                ship_angle += 0.07
            if keys[pygame.K_UP]:
                ship_dx += math.sin(ship_angle) * ship_thrust
                ship_dy -= math.cos(ship_angle) * ship_thrust
                # Play thrust sound
                if sounds_loaded and not thrust_playing:
                    sound_thrust.play(-1)  # Loop indefinitely
                    thrust_playing = True
            else:
                # Stop thrust sound when not thrusting
                if sounds_loaded and thrust_playing:
                    sound_thrust.stop()
                    thrust_playing = False

            ship_dx *= friction
            ship_dy *= friction
            ship_x = (ship_x + ship_dx) % WIDTH
            ship_y = (ship_y + ship_dy) % HEIGHT

            # Update invulnerability timer
            if invulnerable_timer > 0:
                invulnerable_timer -= 1

            # Update bullets
            for bullet in bullets:
                bullet.move()
            bullets = [b for b in bullets if b.lifetime > 0]
            if shoot_cooldown > 0:
                shoot_cooldown -= 1

            # Update heartbeat sound
            if sounds_loaded:
                heartbeat_timer += 1
                # Heartbeat speed depends on number of asteroids (faster with fewer asteroids)
                heartbeat_interval = max(30, 120 - len(asteroids) * 10)
                if heartbeat_timer >= heartbeat_interval:
                    if heartbeat_beat == 0:
                        sound_beat1.play()
                        heartbeat_beat = 1
                    else:
                        sound_beat2.play()
                        heartbeat_beat = 0
                    heartbeat_timer = 0

            # Collision detection: bullets vs asteroids
            new_asteroids = []
            for asteroid in asteroids:
                hit = False
                for bullet in bullets:
                    dist = math.hypot(asteroid.x - bullet.x, asteroid.y - bullet.y)
                    if dist < asteroid.size:
                        bullets.remove(bullet)
                        hit = True
                        break
                if hit:
                    # Add score based on asteroid size and play explosion sound
                    if asteroid.size == ASTEROID_MAX_SIZE:
                        score += SCORE_LARGE_ASTEROID
                        if sounds_loaded:
                            sound_bang_large.play()
                        # Large asteroid breaks into 2 mediums
                        for _ in range(2):
                            new_asteroids.append(Asteroid(asteroid.x, asteroid.y, size=ASTEROID_MEDIUM_SIZE))
                    elif asteroid.size == ASTEROID_MEDIUM_SIZE:
                        score += SCORE_MEDIUM_ASTEROID
                        if sounds_loaded:
                            sound_bang_medium.play()
                        # Medium asteroid breaks into 3-4 smalls
                        for _ in range(random.randint(3, 4)):
                            new_asteroids.append(Asteroid(asteroid.x, asteroid.y, size=ASTEROID_MIN_SIZE))
                    else:  # Small asteroid
                        score += SCORE_SMALL_ASTEROID
                        if sounds_loaded:
                            sound_bang_small.play()
                        # Small asteroid just disappears
                    continue  # Don't add the destroyed asteroid
                new_asteroids.append(asteroid)
            asteroids = new_asteroids

            # Check if all asteroids are destroyed - spawn new wave
            if len(asteroids) == 0:
                for _ in range(random.randint(4, 6)):
                    while True:
                        x = random.randint(0, WIDTH)
                        y = random.randint(0, HEIGHT)
                        if abs(x - ship_x) > 100 and abs(y - ship_y) > 100:
                            break
                    asteroids.append(Asteroid(x, y, size=ASTEROID_MAX_SIZE))

            # Collision detection: ship vs asteroids (only if not invulnerable)
            if invulnerable_timer == 0:
                for asteroid in asteroids:
                    dist = math.hypot(asteroid.x - ship_x, asteroid.y - ship_y)
                    if dist < asteroid.size + SHIP_SIZE // 2:
                        lives -= 1
                        # Stop thrust sound if playing
                        if sounds_loaded and thrust_playing:
                            sound_thrust.stop()
                            thrust_playing = False
                        if lives <= 0:
                            game_over = True
                            # Stop all sounds when game over
                            if sounds_loaded:
                                pygame.mixer.stop()
                        else:
                            # Respawn ship
                            ship_x, ship_y, ship_angle, ship_dx, ship_dy = respawn_ship()
                            invulnerable_timer = INVULNERABILITY_TIME
                            if sounds_loaded:
                                sound_extra_ship.play()
                        break

        screen.fill(BLACK)

        if not game_over:
            for asteroid in asteroids:
                asteroid.move()
                asteroid.draw(screen)
            for bullet in bullets:
                bullet.draw(screen)
            draw_ship(screen, ship_x, ship_y, ship_angle, invulnerable_timer > 0)
            draw_ui(screen, font, score, lives)
        else:
            draw_game_over(screen, font, score)

        pygame.display.flip()
        clock.tick(FPS)

    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()
